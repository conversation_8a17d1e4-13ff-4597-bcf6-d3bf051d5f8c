"use client"

import { memo, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Download,
  Eye,
  Gauge,
  HardDrive,
  Monitor,
  Timer,
  Trash2,
  TrendingUp,
  Zap
} from "lucide-react"
import { usePerformanceMonitoring } from "@/lib/hooks/use-performance-monitoring"

const PerformanceMetricCard = memo(function PerformanceMetricCard({
  title,
  value,
  unit,
  threshold,
  icon: Icon,
  status
}: {
  title: string
  value: number
  unit: string
  threshold: number
  icon: any
  status: 'good' | 'warning' | 'error'
}) {
  const getStatusColor = () => {
    switch (status) {
      case 'good': return 'text-emerald-600'
      case 'warning': return 'text-orange-600'
      case 'error': return 'text-red-600'
      default: return 'text-muted-foreground'
    }
  }

  const getStatusBadge = () => {
    switch (status) {
      case 'good': return <Badge variant="outline" className="bg-emerald-50 text-emerald-700">Good</Badge>
      case 'warning': return <Badge variant="outline" className="bg-orange-50 text-orange-700">Warning</Badge>
      case 'error': return <Badge variant="outline" className="bg-red-50 text-red-700">Error</Badge>
    }
  }

  const progressValue = Math.min((value / threshold) * 100, 100)

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          <span className={getStatusColor()}>
            {value.toFixed(value < 10 ? 1 : 0)}{unit}
          </span>
        </div>
        <div className="flex items-center justify-between mt-2">
          {getStatusBadge()}
          <span className="text-xs text-muted-foreground">
            Threshold: {threshold}{unit}
          </span>
        </div>
        <Progress 
          value={progressValue} 
          className={`mt-2 ${status === 'error' ? 'bg-red-100' : status === 'warning' ? 'bg-orange-100' : 'bg-emerald-100'}`}
        />
      </CardContent>
    </Card>
  )
})

const AlertsList = memo(function AlertsList({ 
  alerts, 
  onClear 
}: { 
  alerts: any[], 
  onClear: () => void 
}) {
  if (alerts.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <CheckCircle className="h-12 w-12 mx-auto mb-4 text-emerald-500" />
        <p>No performance alerts</p>
        <p className="text-sm">All metrics are within acceptable ranges</p>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Performance Alerts</h3>
        <Button variant="outline" size="sm" onClick={onClear}>
          <Trash2 className="h-4 w-4 mr-2" />
          Clear All
        </Button>
      </div>
      {alerts.map((alert, index) => (
        <Alert key={index} variant={alert.type === 'error' ? 'destructive' : 'default'}>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle className="capitalize">{alert.type}</AlertTitle>
          <AlertDescription>
            {alert.message}
            <div className="text-xs mt-1 opacity-75">
              {new Date(alert.timestamp).toLocaleTimeString()}
            </div>
          </AlertDescription>
        </Alert>
      ))}
    </div>
  )
})

const ChartPerformanceView = memo(function ChartPerformanceView({ 
  chartRenderTimes 
}: { 
  chartRenderTimes: Record<string, number> 
}) {
  const chartEntries = Object.entries(chartRenderTimes)

  if (chartEntries.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <Activity className="h-12 w-12 mx-auto mb-4" />
        <p>No chart performance data yet</p>
        <p className="text-sm">Chart render times will appear here</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Chart Render Performance</h3>
      {chartEntries.map(([chartName, renderTime]) => {
        const status = renderTime > 500 ? 'error' : renderTime > 300 ? 'warning' : 'good'
        const statusColor = status === 'error' ? 'text-red-600' : status === 'warning' ? 'text-orange-600' : 'text-emerald-600'
        
        return (
          <div key={chartName} className="flex items-center justify-between p-3 border rounded-lg">
            <div>
              <div className="font-medium">{chartName}</div>
              <div className="text-sm text-muted-foreground">Chart component</div>
            </div>
            <div className="text-right">
              <div className={`font-bold ${statusColor}`}>
                {renderTime.toFixed(0)}ms
              </div>
              <div className="text-xs text-muted-foreground">
                Target: &lt;500ms
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
})

export const PerformanceMonitor = memo(function PerformanceMonitor() {
  const {
    metrics,
    alerts,
    getPerformanceScore,
    exportMetrics,
    clearAlerts,
    thresholds
  } = usePerformanceMonitoring()

  const [isVisible, setIsVisible] = useState(false)

  const performanceScore = getPerformanceScore()

  const getMetricStatus = (value: number, threshold: number) => {
    if (value <= threshold * 0.7) return 'good'
    if (value <= threshold) return 'warning'
    return 'error'
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-emerald-600'
    if (score >= 70) return 'text-orange-600'
    return 'text-red-600'
  }

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsVisible(true)}
          className="shadow-lg"
        >
          <Gauge className="h-4 w-4 mr-2" />
          Performance Monitor
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-[80vh] overflow-hidden z-50">
      <Card className="shadow-xl">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Performance Monitor</CardTitle>
              <CardDescription>Real-time performance metrics</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-right">
                <div className={`text-2xl font-bold ${getScoreColor(performanceScore)}`}>
                  {performanceScore}
                </div>
                <div className="text-xs text-muted-foreground">Score</div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsVisible(false)}
              >
                ×
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="max-h-96 overflow-y-auto">
          <Tabs defaultValue="metrics" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="metrics">Metrics</TabsTrigger>
              <TabsTrigger value="charts">Charts</TabsTrigger>
              <TabsTrigger value="alerts">
                Alerts
                {alerts.length > 0 && (
                  <Badge variant="destructive" className="ml-1 h-5 w-5 p-0 text-xs">
                    {alerts.length}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="metrics" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-3">
                <PerformanceMetricCard
                  title="Load Time"
                  value={metrics.loadTime}
                  unit="ms"
                  threshold={thresholds.loadTime}
                  icon={Timer}
                  status={getMetricStatus(metrics.loadTime, thresholds.loadTime)}
                />
                <PerformanceMetricCard
                  title="Memory Usage"
                  value={metrics.memoryUsage}
                  unit="MB"
                  threshold={thresholds.memoryUsage}
                  icon={HardDrive}
                  status={getMetricStatus(metrics.memoryUsage, thresholds.memoryUsage)}
                />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Components: {metrics.componentCount}</span>
                  <span>Errors: {metrics.errorCount}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Browser: {metrics.isFirefox ? 'Firefox' : 'Other'}</span>
                  <Button variant="outline" size="sm" onClick={exportMetrics}>
                    <Download className="h-3 w-3 mr-1" />
                    Export
                  </Button>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="charts" className="mt-4">
              <ChartPerformanceView chartRenderTimes={metrics.chartRenderTimes} />
            </TabsContent>
            
            <TabsContent value="alerts" className="mt-4">
              <AlertsList alerts={alerts} onClear={clearAlerts} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
})

PerformanceMonitor.displayName = "PerformanceMonitor"
